<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title><?php echo isset($page_title) ? $page_title : 'Dashboard' ?> - Pandeglang Jobs Admin</title>

    <!-- Custom fonts for this template -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Custom admin styles -->
    <link href="<?php echo base_url('assets/admin/css/admin.css') ?>" rel="stylesheet">

    <!-- Fallback CSS jika file tidak ditemukan -->
    <style>
        /* Basic admin styling fallback */
        .sidebar { background: #343a40; min-height: 100vh; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background: #495057; }
        .content-wrapper { margin-left: 250px; padding: 20px; }
        @media (max-width: 768px) {
            .content-wrapper { margin-left: 0; }
        }
    </style>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        body {
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fc;
        }

        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            width: 250px;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,.8);
            padding: 1rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.1);
            border-left-color: #3498db;
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(52, 152, 219, 0.2);
            border-left-color: #3498db;
        }

        .sidebar .nav-link i {
            margin-right: 0.75rem;
            width: 16px;
            text-align: center;
        }

        .sidebar-brand {
            padding: 1.5rem 1rem;
            text-decoration: none;
            color: #fff;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .sidebar-brand:hover {
            color: #3498db;
            text-decoration: none;
        }

        .sidebar-brand-icon {
            margin-right: 0.5rem;
        }

        .sidebar-brand-icon img {
            border-radius: 4px;
            background-color: rgba(255,255,255,0.1);
            padding: 2px;
        }

        .sidebar-divider {
            border-top: 1px solid rgba(255,255,255,0.15);
            margin: 1rem 0;
        }

        .sidebar-heading {
            color: rgba(255,255,255,0.4);
            font-size: 0.65rem;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            padding: 0 1.5rem;
            margin-bottom: 0.5rem;
        }

        .content-wrapper {
            margin-left: 250px;
            min-height: 100vh;
        }

        .topbar {
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .dropdown-menu {
            border: 0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .dropdown-item {
            padding: 0.5rem 1.5rem;
            font-size: 0.85rem;
        }

        .dropdown-item:hover {
            background-color: #f8f9fc;
        }

        .nav-link.dropdown-toggle {
            color: #5a5c69 !important;
            padding: 0.75rem 1rem;
        }

        .nav-link.dropdown-toggle:hover {
            color: #3a3b45 !important;
        }

        .topbar {
            height: 4.375rem;
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card {
            border: 1px solid #e3e6f0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border-radius: 0.35rem;
        }

        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }

        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .text-primary {
            color: #3498db !important;
        }

        .bg-primary {
            background-color: #3498db !important;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
            }
            .content-wrapper {
                margin-left: 0;
            }
            .sidebar.show {
                margin-left: 0;
            }
        }

        /* Notification Styles */
        .badge-counter {
            font-size: 0.7rem;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-circle {
            height: 2.5rem;
            width: 2.5rem;
            border-radius: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dropdown-menu {
            min-width: 20rem;
        }

        .dropdown-item:hover {
            background-color: #f8f9fc;
        }
    </style>
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <nav class="sidebar">
            <!-- Sidebar Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="<?= site_url('admin') ?>">
                <div class="sidebar-brand-icon">
                    <img src="<?= base_url('assets/img/logo_pandeglang.png') ?>" height="35" alt="Logo Disnaker Pandeglang" style="margin-right: 10px;">
                </div>
                <div class="sidebar-brand-text">Pandeglang Jobs Admin</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Navigation -->
            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link <?= $this->uri->segment(2) == '' ? 'active' : '' ?>" href="<?= site_url('admin') ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <!-- Data Lowongan -->
                <li class="nav-item">
                    <a class="nav-link <?= $this->uri->segment(2) == 'lowongan' ? 'active' : '' ?>" href="<?= site_url('admin/lowongan') ?>">
                        <i class="fas fa-briefcase"></i>
                        <span>Data Lowongan</span>
                    </a>
                </li>

                <!-- Rekrutmen -->
                <li class="nav-item">
                    <a class="nav-link <?= $this->uri->segment(2) == 'rekrutmen' ? 'active' : '' ?>" href="<?= site_url('admin/rekrutmen') ?>">
                        <i class="fas fa-users"></i>
                        <span>Rekrutmen</span>
                    </a>
                </li>



                <!-- Logout -->
                <li class="nav-item">
                    <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#logoutModal">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Topbar -->
            <nav class="navbar navbar-expand navbar-light topbar mb-4 static-top">
                <!-- Sidebar Toggle (Mobile) -->
                <button class="btn btn-link d-md-none rounded-circle me-3" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>

                <!-- Page Title -->
                <div class="navbar-nav me-auto ms-3">
                    <h1 class="h3 mb-0 text-gray-800"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h1>
                </div>

                <!-- Topbar Navbar -->
                <ul class="navbar-nav ms-auto">
                    <!-- Notifications -->
                    <li class="nav-item dropdown no-arrow mx-1">
                        <a class="nav-link dropdown-toggle" href="#" id="alertsDropdown" role="button"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-bell fa-fw"></i>
                            <!-- Counter - Alerts -->
                            <?php
                            // Get unread notifications count
                            $unread_count = 0;
                            if ($this->db->table_exists('notifications')) {
                                $unread_count = $this->db->where('is_read', 0)->count_all_results('notifications');
                            }
                            ?>
                            <?php if ($unread_count > 0): ?>
                            <span class="badge bg-danger badge-counter position-absolute top-0 start-100 translate-middle"><?= $unread_count > 9 ? '9+' : $unread_count ?></span>
                            <?php endif; ?>
                        </a>
                        <!-- Dropdown - Alerts -->
                        <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="alertsDropdown">
                            <h6 class="dropdown-header">
                                Notifikasi Terbaru
                            </h6>
                            <?php
                            // Get recent notifications
                            if ($this->db->table_exists('notifications')) {
                                $notifications = $this->db->order_by('created_at', 'DESC')->limit(5)->get('notifications')->result();
                                if (!empty($notifications)):
                                    foreach ($notifications as $notif):
                            ?>
                            <a class="dropdown-item d-flex align-items-center" href="<?= site_url('admin/rekrutmen') ?>">
                                <div class="me-3">
                                    <div class="icon-circle <?= $notif->is_read ? 'bg-secondary' : 'bg-primary' ?>">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-gray-500"><?= date('d M Y H:i', strtotime($notif->created_at)) ?></div>
                                    <span class="font-weight-bold"><?= substr($notif->message, 0, 50) ?>...</span>
                                </div>
                            </a>
                            <?php
                                    endforeach;
                                else:
                            ?>
                            <a class="dropdown-item text-center small text-gray-500" href="#">Tidak ada notifikasi</a>
                            <?php
                                endif;
                            } else {
                            ?>
                            <a class="dropdown-item text-center small text-gray-500" href="#">Tidak ada notifikasi</a>
                            <?php } ?>
                            <a class="dropdown-item text-center small text-gray-500" href="<?= site_url('admin/rekrutmen') ?>">Lihat Semua</a>
                        </div>
                    </li>

                    <!-- User Info -->
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="<?= site_url('admin/profil') ?>">
                            <span class="me-2 d-none d-lg-inline text-gray-600 small">
                                <?= $this->session->userdata('admin_name') ? $this->session->userdata('admin_name') : 'Administrator' ?>
                            </span>
                            <i class="fas fa-user-circle fa-fw text-gray-400"></i>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid px-4">
