<?php
/**
 * Konfigurasi khusus untuk Ngrok
 * Jalankan file ini untuk auto-update konfigurasi
 */

echo "<h1>🔧 Auto Config untuk Ngrok</h1>";

// Deteksi URL saat ini
$protocol = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? "https" : "http");
$host = $_SERVER['HTTP_HOST'];
$current_url = $protocol . '://' . $host . '/';

echo "<p><strong>Detected URL:</strong> $current_url</p>";

// Path ke file config
$config_file = 'application/config/config.php';

if (!file_exists($config_file)) {
    echo "<p>❌ Config file not found: $config_file</p>";
    exit;
}

// Baca file config
$config_content = file_get_contents($config_file);

if ($config_content === false) {
    echo "<p>❌ Cannot read config file</p>";
    exit;
}

// Update base_url
$new_config = preg_replace(
    '/\$config\[\'base_url\'\]\s*=\s*[^;]+;/',
    "\$config['base_url'] = '$current_url';",
    $config_content
);

// Backup original
$backup_file = $config_file . '.backup.' . date('Y-m-d-H-i-s');
if (file_put_contents($backup_file, $config_content)) {
    echo "<p>✅ Backup created: $backup_file</p>";
}

// Write new config
if (file_put_contents($config_file, $new_config)) {
    echo "<p>✅ Config updated successfully!</p>";
    echo "<p><strong>New base_url:</strong> $current_url</p>";
    
    // Test the new config
    echo "<h2>🧪 Testing New Configuration</h2>";
    
    // Clear any existing config cache
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
    
    // Include the updated config
    unset($config);
    include($config_file);
    
    echo "<p><strong>Loaded base_url:</strong> " . (isset($config['base_url']) ? $config['base_url'] : 'Not set') . "</p>";
    
    if (isset($config['base_url']) && $config['base_url'] === $current_url) {
        echo "<p>✅ Configuration verified!</p>";
        
        echo "<h3>🔗 Test Links:</h3>";
        echo "<ul>";
        echo "<li><a href='index.php'>Direct: index.php</a></li>";
        echo "<li><a href='admin'>Clean URL: admin</a></li>";
        echo "<li><a href='lowongan'>Clean URL: lowongan</a></li>";
        echo "<li><a href='index.php/admin'>With index.php: admin</a></li>";
        echo "</ul>";
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ Configuration Complete!</h4>";
        echo "<p>Your Portal Loker should now work properly with Ngrok.</p>";
        echo "<p><strong>Main URL:</strong> <a href='$current_url'>$current_url</a></p>";
        echo "<p><strong>Admin Panel:</strong> <a href='{$current_url}admin'>{$current_url}admin</a></p>";
        echo "</div>";
        
    } else {
        echo "<p>❌ Configuration verification failed</p>";
    }
    
} else {
    echo "<p>❌ Failed to update config file</p>";
    echo "<p>Please manually update the base_url in $config_file to: <code>$current_url</code></p>";
}

// Additional fixes for common issues
echo "<h2>🔧 Additional Fixes</h2>";

// Check .htaccess
if (file_exists('.htaccess')) {
    echo "<p>✅ .htaccess file exists</p>";
    
    $htaccess_content = file_get_contents('.htaccess');
    if (strpos($htaccess_content, 'RewriteEngine On') !== false) {
        echo "<p>✅ URL rewriting enabled</p>";
    } else {
        echo "<p>❌ URL rewriting not properly configured</p>";
    }
} else {
    echo "<p>❌ .htaccess file missing</p>";
    
    // Create basic .htaccess
    $htaccess_content = "RewriteEngine On\n";
    $htaccess_content .= "RewriteCond %{REQUEST_FILENAME} !-f\n";
    $htaccess_content .= "RewriteCond %{REQUEST_FILENAME} !-d\n";
    $htaccess_content .= "RewriteRule ^(.*)$ index.php?/\$1 [L,QSA]\n";
    
    if (file_put_contents('.htaccess', $htaccess_content)) {
        echo "<p>✅ Created basic .htaccess file</p>";
    } else {
        echo "<p>❌ Failed to create .htaccess file</p>";
    }
}

// Check database config
echo "<h3>🗄️ Database Check</h3>";
$db_config_file = 'application/config/database.php';
if (file_exists($db_config_file)) {
    echo "<p>✅ Database config exists</p>";
    
    include($db_config_file);
    if (isset($db['default'])) {
        $hostname = $db['default']['hostname'];
        $database = $db['default']['database'];
        echo "<p><strong>Database Host:</strong> $hostname</p>";
        echo "<p><strong>Database Name:</strong> $database</p>";
        
        // Test connection
        try {
            $conn = new mysqli(
                $db['default']['hostname'],
                $db['default']['username'],
                $db['default']['password'],
                $db['default']['database']
            );
            
            if ($conn->connect_error) {
                echo "<p>❌ Database connection failed: " . $conn->connect_error . "</p>";
            } else {
                echo "<p>✅ Database connection successful</p>";
                $conn->close();
            }
        } catch (Exception $e) {
            echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<p>❌ Database config not found</p>";
}

echo "<hr>";
echo "<p><small>Auto-config completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
code { background: #f4f4f4; padding: 2px 6px; border-radius: 3px; }
ul, ol { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
