<?php
/**
 * Debug file untuk troubleshooting masalah hosting
 * Upload file ini ke root hosting dan akses via browser
 */

echo "<h1>🔍 <PERSON> Loker - Debug Hosting</h1>";
echo "<hr>";

// 1. Server Information
echo "<h2>📊 Server Information</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><td><strong>PHP Version</strong></td><td>" . phpversion() . "</td></tr>";
echo "<tr><td><strong>Server Software</strong></td><td>" . $_SERVER['SERVER_SOFTWARE'] . "</td></tr>";
echo "<tr><td><strong>Document Root</strong></td><td>" . $_SERVER['DOCUMENT_ROOT'] . "</td></tr>";
echo "<tr><td><strong>Script Name</strong></td><td>" . $_SERVER['SCRIPT_NAME'] . "</td></tr>";
echo "<tr><td><strong>HTTP Host</strong></td><td>" . $_SERVER['HTTP_HOST'] . "</td></tr>";
echo "<tr><td><strong>Request URI</strong></td><td>" . $_SERVER['REQUEST_URI'] . "</td></tr>";
echo "<tr><td><strong>HTTPS</strong></td><td>" . (isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'Not Set') . "</td></tr>";
echo "</table>";

// 2. PHP Extensions
echo "<h2>🔧 PHP Extensions</h2>";
$required_extensions = ['mysqli', 'pdo', 'gd', 'curl', 'mbstring', 'zip'];
echo "<table border='1' cellpadding='5'>";
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ Loaded' : '❌ Missing';
    echo "<tr><td><strong>$ext</strong></td><td>$status</td></tr>";
}
echo "</table>";

// 3. File System Check
echo "<h2>📁 File System Check</h2>";
$files_to_check = [
    'index.php',
    '.htaccess',
    'application/config/config.php',
    'application/config/database.php',
    'application/controllers/Admin.php',
    'assets/css/',
    'assets/js/',
    'assets/images/'
];

echo "<table border='1' cellpadding='5'>";
foreach ($files_to_check as $file) {
    $exists = file_exists($file) ? '✅ Exists' : '❌ Missing';
    $readable = is_readable($file) ? '✅ Readable' : '❌ Not Readable';
    echo "<tr><td><strong>$file</strong></td><td>$exists</td><td>$readable</td></tr>";
}
echo "</table>";

// 4. CodeIgniter Configuration
echo "<h2>⚙️ CodeIgniter Configuration</h2>";
if (file_exists('application/config/config.php')) {
    include_once('application/config/config.php');
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><td><strong>Base URL</strong></td><td>" . (isset($config['base_url']) ? $config['base_url'] : 'Not Set') . "</td></tr>";
    echo "<tr><td><strong>Index Page</strong></td><td>" . (isset($config['index_page']) ? $config['index_page'] : 'Not Set') . "</td></tr>";
    echo "<tr><td><strong>URI Protocol</strong></td><td>" . (isset($config['uri_protocol']) ? $config['uri_protocol'] : 'Not Set') . "</td></tr>";
    echo "</table>";
} else {
    echo "<p>❌ Config file not found!</p>";
}

// 5. Database Connection Test
echo "<h2>🗄️ Database Connection Test</h2>";
if (file_exists('application/config/database.php')) {
    include_once('application/config/database.php');
    
    if (isset($db['default'])) {
        $hostname = $db['default']['hostname'];
        $username = $db['default']['username'];
        $password = $db['default']['password'];
        $database = $db['default']['database'];
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><td><strong>Hostname</strong></td><td>$hostname</td></tr>";
        echo "<tr><td><strong>Username</strong></td><td>$username</td></tr>";
        echo "<tr><td><strong>Database</strong></td><td>$database</td></tr>";
        
        try {
            $conn = new mysqli($hostname, $username, $password, $database);
            if ($conn->connect_error) {
                echo "<tr><td><strong>Connection</strong></td><td>❌ Failed: " . $conn->connect_error . "</td></tr>";
            } else {
                echo "<tr><td><strong>Connection</strong></td><td>✅ Success</td></tr>";
                
                // Test tables
                $tables = ['user', 'lowongan_kerja', 'aplikasi_kerja'];
                foreach ($tables as $table) {
                    $result = $conn->query("SHOW TABLES LIKE '$table'");
                    $exists = $result && $result->num_rows > 0 ? '✅ Exists' : '❌ Missing';
                    echo "<tr><td><strong>Table: $table</strong></td><td>$exists</td></tr>";
                }
                $conn->close();
            }
        } catch (Exception $e) {
            echo "<tr><td><strong>Connection</strong></td><td>❌ Error: " . $e->getMessage() . "</td></tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ Database config file not found!</p>";
}

// 6. URL Generation Test
echo "<h2>🔗 URL Generation Test</h2>";
$protocol = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? "https" : "http");
$host = $_SERVER['HTTP_HOST'];
$script_path = str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);

$base_url_auto = $protocol . '://' . $host . $script_path;
$base_url_ngrok = $protocol . '://' . $host . '/';

echo "<table border='1' cellpadding='5'>";
echo "<tr><td><strong>Auto-detected Base URL</strong></td><td>$base_url_auto</td></tr>";
echo "<tr><td><strong>Ngrok Base URL</strong></td><td>$base_url_ngrok</td></tr>";
echo "</table>";

// 7. Test Links
echo "<h2>🔗 Test Links</h2>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>index.php (Direct)</a></li>";
echo "<li><a href='admin' target='_blank'>admin (Clean URL)</a></li>";
echo "<li><a href='index.php/admin' target='_blank'>index.php/admin (With index.php)</a></li>";
echo "<li><a href='lowongan' target='_blank'>lowongan (Clean URL)</a></li>";
echo "<li><a href='assets/css/' target='_blank'>assets/css/ (Static files)</a></li>";
echo "</ul>";

// 8. Recommendations
echo "<h2>💡 Recommendations</h2>";
echo "<div style='background: #f0f8ff; padding: 10px; border: 1px solid #ccc;'>";

if (strpos($host, 'ngrok') !== false) {
    echo "<h3>🔧 Ngrok Configuration:</h3>";
    echo "<p>Detected ngrok URL. Update your config:</p>";
    echo "<code>\$config['base_url'] = '$base_url_ngrok';</code><br>";
}

if (!extension_loaded('mysqli')) {
    echo "<h3>❌ Missing mysqli extension</h3>";
    echo "<p>Contact your hosting provider to enable mysqli extension.</p>";
}

if (!file_exists('.htaccess')) {
    echo "<h3>❌ Missing .htaccess file</h3>";
    echo "<p>Upload .htaccess file to enable clean URLs.</p>";
}

echo "<h3>📝 Common Fixes:</h3>";
echo "<ol>";
echo "<li>Update base_url in application/config/config.php</li>";
echo "<li>Check .htaccess file exists and has correct content</li>";
echo "<li>Verify file permissions (folders: 755, files: 644)</li>";
echo "<li>Test database connection</li>";
echo "<li>Clear browser cache</li>";
echo "</ol>";

echo "</div>";

// 9. PHP Info (commented out for security)
// echo "<h2>📋 PHP Info</h2>";
// echo "<p><a href='?phpinfo=1'>Show PHP Info</a></p>";
// if (isset($_GET['phpinfo'])) {
//     phpinfo();
// }

echo "<hr>";
echo "<p><small>Debug completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { text-align: left; padding: 8px; }
tr:nth-child(even) { background-color: #f2f2f2; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
