<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Portal Loker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .test-box { border: 2px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Portal Loker Hosting</h1>
        
        <?php
        // Test 1: PHP Basic
        echo '<div class="test-box success">';
        echo '<h3>✅ PHP Working</h3>';
        echo '<p>PHP Version: ' . phpversion() . '</p>';
        echo '<p>Server: ' . $_SERVER['HTTP_HOST'] . '</p>';
        echo '</div>';
        
        // Test 2: File Structure
        $files = ['index.php', '.htaccess', 'application/', 'assets/'];
        $all_files_exist = true;
        
        echo '<div class="test-box">';
        echo '<h3>📁 File Structure</h3>';
        foreach ($files as $file) {
            $exists = file_exists($file);
            if (!$exists) $all_files_exist = false;
            $icon = $exists ? '✅' : '❌';
            echo "<p>$icon $file</p>";
        }
        echo '</div>';
        
        // Test 3: CodeIgniter Config
        echo '<div class="test-box">';
        echo '<h3>⚙️ CodeIgniter Config</h3>';
        
        if (file_exists('application/config/config.php')) {
            include_once('application/config/config.php');
            echo '<p>✅ Config file loaded</p>';
            echo '<p><strong>Base URL:</strong> ' . (isset($config['base_url']) ? $config['base_url'] : 'Not set') . '</p>';
            
            // Auto-detect correct base URL
            $protocol = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? "https" : "http");
            $host = $_SERVER['HTTP_HOST'];
            $path = str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
            $suggested_url = $protocol . '://' . $host . $path;
            
            echo '<p><strong>Suggested Base URL:</strong> ' . $suggested_url . '</p>';
            
            if (strpos($host, 'ngrok') !== false) {
                $ngrok_url = $protocol . '://' . $host . '/';
                echo '<p><strong>Ngrok URL:</strong> ' . $ngrok_url . '</p>';
                echo '<div class="alert alert-info">Detected ngrok! Use: <code>' . $ngrok_url . '</code></div>';
            }
        } else {
            echo '<p>❌ Config file not found</p>';
        }
        echo '</div>';
        
        // Test 4: Database
        echo '<div class="test-box">';
        echo '<h3>🗄️ Database Connection</h3>';
        
        if (file_exists('application/config/database.php')) {
            include_once('application/config/database.php');
            
            if (isset($db['default'])) {
                try {
                    $conn = new mysqli(
                        $db['default']['hostname'],
                        $db['default']['username'],
                        $db['default']['password'],
                        $db['default']['database']
                    );
                    
                    if ($conn->connect_error) {
                        echo '<p>❌ Database connection failed: ' . $conn->connect_error . '</p>';
                    } else {
                        echo '<p>✅ Database connected successfully</p>';
                        echo '<p><strong>Database:</strong> ' . $db['default']['database'] . '</p>';
                        $conn->close();
                    }
                } catch (Exception $e) {
                    echo '<p>❌ Database error: ' . $e->getMessage() . '</p>';
                }
            }
        } else {
            echo '<p>❌ Database config not found</p>';
        }
        echo '</div>';
        
        // Test 5: URL Rewriting
        echo '<div class="test-box">';
        echo '<h3>🔗 URL Testing</h3>';
        echo '<p><a href="index.php" class="btn btn-primary">Test index.php</a></p>';
        echo '<p><a href="admin" class="btn btn-secondary">Test Clean URL (admin)</a></p>';
        echo '<p><a href="index.php/admin" class="btn btn-info">Test with index.php</a></p>';
        echo '</div>';
        
        // Test 6: Assets
        echo '<div class="test-box">';
        echo '<h3>🎨 Assets Test</h3>';
        $css_url = $suggested_url . 'assets/admin/css/admin.css';
        echo '<p>CSS URL: <a href="' . $css_url . '" target="_blank">' . $css_url . '</a></p>';
        
        $img_url = $suggested_url . 'assets/img/logo_pandeglang.png';
        echo '<p>Image URL: <a href="' . $img_url . '" target="_blank">' . $img_url . '</a></p>';
        echo '</div>';
        
        // Recommendations
        echo '<div class="test-box">';
        echo '<h3>💡 Quick Fixes</h3>';
        echo '<ol>';
        
        if (strpos($host, 'ngrok') !== false) {
            echo '<li><strong>For Ngrok:</strong> Update base_url to: <code>https://' . $host . '/</code></li>';
        }
        
        echo '<li>Make sure .htaccess file exists in root directory</li>';
        echo '<li>Check file permissions: folders 755, files 644</li>';
        echo '<li>Verify database credentials in application/config/database.php</li>';
        echo '<li>Clear browser cache and try again</li>';
        echo '</ol>';
        echo '</div>';
        ?>
        
        <div class="test-box">
            <h3>🚀 Next Steps</h3>
            <p>If all tests pass, try accessing:</p>
            <ul>
                <li><a href="index.php">Portal Homepage</a></li>
                <li><a href="admin">Admin Panel</a></li>
                <li><a href="lowongan">Job Listings</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
